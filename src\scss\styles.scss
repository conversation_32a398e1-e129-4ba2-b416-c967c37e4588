// Import our custom variables first, with namespace
@use "./variables" as v;
@use "sass:color";

// Import styles with default layout.
@use "@coreui/coreui/scss/coreui" with (
  $enable-deprecation-messages: false,
  $primary: v.$primary,
  $secondary: v.$secondary,
  $success: v.$success
);

// Import Chart.js custom tooltips styles
@use "@coreui/chartjs/scss/coreui-chartjs";

// Import our shared styles and utilities
@use "./_shared-styles" as shared;

// Custom styles for this theme
@use "./theme.scss";

// Some temp fixes
//@use "fixes";

// If you want to add custom CSS you can put it here.
@use "./custom.scss";

// Professional Button Styles
@use "./buttons.scss";

// Button Standardization
@use "./button-standards.scss";

// Professional Sidebar Styles
@use "./sidebar.scss";

// Professional Header Styles
@use "./header.scss";

// Professional Pagination Styles
@use "./pagination.scss";

// Professional Table Styles
@use "./_tables.scss" as tables;

// Theme System (Dark/Light Mode)
@use "./_theme-system.scss";

// Custom Leaflet styling
@use "./_leaflet-combined.scss";

// Examples
// We use those styles to show code examples, you should remove them in your application.
@use "./examples.scss";

// Global UI/UX Styles
@use "./global-styles.scss";

// Import Custom Scrollbar
@use "./_scrollbar.scss" as scrollbar;

// Import Leaflet styles
@use './leaflet' as leaflet-custom;

// Import custom styles (must be last)
@use './_custom-styles';

// Bootstrap Icons are imported via angular.json

