::ng-deep .c-modal.fade-in {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1050 !important;
  background: rgba(0,0,0,0.4) !important;
}

::ng-deep .c-modal-dialog {
  margin: auto !important;
  max-width: 400px;
  width: 100%;
}

.actions-cell {
  white-space: nowrap;

  .btn {
    margin-right: 0.25rem;
  }
}

.map-wrapper {
  animation: fadeIn 0.5s ease;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 400px;
  width: 100%;

  &.d-none {
    height: 0;
    opacity: 0;
  }

  &.d-block {
    height: 400px;
    opacity: 1;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    border-color: var(--cui-primary);
  }
}

.btn-outline-primary {
  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
}

// Animation for coordinate display
.row.g-2 {
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}
