:host {
  display: block;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-toggle {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 10;
}

.lang-switch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  i {
    font-size: 1.1rem;
  }

  .lang-text {
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 2rem 1rem;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  padding: 2rem 2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 420px;
  width: 100%;

  &:hover {
    transform: translateY(-3px);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.3);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo-container {
  margin-bottom: 1rem;
}

.logo-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  box-shadow: 0 8px 24px rgba(205, 44, 78, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05) rotate(3deg);
    box-shadow: 0 12px 32px rgba(205, 44, 78, 0.4);
  }
}

.login-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
  font-weight: 400;
}

.login-form-container {
  margin-bottom: 1.5rem;
}

.login-form .form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;

  i {
    color: var(--theme-primary, #CD2C4E);
    font-size: 1rem;
  }
}

.input-wrapper {
  position: relative;
}

.modern-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  color: #2d3748;

  &::placeholder {
    color: #a0aec0;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    border-color: var(--theme-primary, #CD2C4E);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(205, 44, 78, 0.15);
  }

  &.error {
    border-color: var(--theme-danger, #dc3545);
    background: rgba(254, 215, 215, 0.5);
  }
}

.password-wrapper {
  position: relative;
}

.password-input {
  padding-right: 3rem !important;
}

.password-toggle-btn {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  z-index: 2;

  &:hover {
    color: var(--theme-primary, #CD2C4E);
    background: rgba(205, 44, 78, 0.1);
  }

  &:focus {
    outline: 2px solid var(--theme-primary, #CD2C4E);
    outline-offset: 2px;
  }

  i {
    font-size: 1rem;
  }
}

.input-focus-border {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.modern-input:focus + .password-toggle-btn + .input-focus-border,
.modern-input:focus + .input-focus-border {
  width: 100%;
}

.field-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-danger, #dc3545);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  font-weight: 500;

  i {
    font-size: 0.9rem;
    flex-shrink: 0;
  }
}

.error-alert {
  background: rgba(254, 215, 215, 0.8);
  border: 1px solid rgba(252, 129, 129, 0.5);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;

  &.phone-error {
    background: rgba(255, 243, 205, 0.8);
    border-color: rgba(255, 193, 7, 0.5);
  }
}

.error-icon {
  color: var(--theme-danger, #dc3545);
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.error-content {
  flex: 1;
}

.error-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--theme-danger, #dc3545);
  margin: 0 0 0.25rem 0;
}

.error-message {
  font-size: 0.85rem;
  color: #dc3545;
  margin: 0;
  line-height: 1.4;
}

.login-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(205, 44, 78, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 12px 28px rgba(205, 44, 78, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  &.loading {
    .button-content {
      opacity: 0;
    }

    .button-loader {
      opacity: 1;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
  .language-toggle {
    top: 1.5rem;
    right: 1.5rem;
  }

  .lang-switch-btn {
    padding: 0.625rem 0.875rem;
    font-size: 0.85rem;

    i {
      font-size: 1rem;
    }
  }

  .login-card {
    padding: 1.5rem 1.25rem;
    margin: 1rem;
    max-width: 380px;
  }

  .logo-icon {
    width: 56px;
    height: 56px;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .login-subtitle {
    font-size: 0.85rem;
  }

  .modern-input {
    padding: 0.75rem 0.875rem;
    font-size: 0.9rem;
  }

  .password-input {
    padding-right: 2.75rem !important;
  }

  .login-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }
}

[data-coreui-theme="dark"] {
  .login-container {
    background: linear-gradient(135deg, var(--theme-bg-primary, #1a1d23) 0%, var(--theme-bg-secondary, #2d3748) 100%);
  }

  .lang-switch-btn {
    background: rgba(var(--theme-bg-tertiary, 55, 65, 81), 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--theme-text-primary, #f7fafc);

    &:hover {
      background: rgba(var(--theme-bg-tertiary, 55, 65, 81), 0.95);
      border-color: rgba(255, 255, 255, 0.2);
    }
  }

  .login-card {
    background: rgba(var(--theme-bg-secondary, 45, 55, 72), 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .login-title {
    color: var(--theme-text-primary, #f7fafc);
  }

  .login-subtitle {
    color: var(--theme-text-muted, #a0aec0);
  }

  .form-label {
    color: var(--theme-text-secondary, #e2e8f0);
  }

  .modern-input {
    background: rgba(var(--theme-bg-tertiary, 55, 65, 81), 0.8);
    border-color: var(--theme-border-color, #4a5568);
    color: var(--theme-text-primary, #f7fafc);

    &::placeholder {
      color: var(--theme-text-muted, #718096);
    }

    &:focus {
      background: rgba(var(--theme-bg-tertiary, 55, 65, 81), 0.95);
      border-color: var(--theme-primary, #CD2C4E);
    }

    &.error {
      border-color: var(--theme-danger, #dc3545);
      background: rgba(220, 53, 69, 0.1);
    }
  }

  .password-toggle-btn {
    color: var(--theme-text-muted, #718096);

    &:hover {
      color: var(--theme-primary, #CD2C4E);
      background: rgba(205, 44, 78, 0.1);
    }
  }

  .error-alert {
    background: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.3);

    &.phone-error {
      background: rgba(255, 193, 7, 0.1);
      border-color: rgba(255, 193, 7, 0.3);
    }
  }

  .error-title {
    color: var(--theme-danger, #dc3545);
  }

  .error-message {
    color: var(--theme-danger-hover, #c82333);
  }
}
