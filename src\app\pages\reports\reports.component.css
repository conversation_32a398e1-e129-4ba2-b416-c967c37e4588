/* ===== PAGE STYLING ===== */
.page-title {
  color: var(--bs-primary, #0d6efd);
  font-weight: 700;
  margin: 0;
}

.page-subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
}

/* ===== FILTER FORM LIKE REQUESTS COMPONENT ===== */
.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  padding: 18px 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.filter-form label {
  font-weight: 500;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-form input[type="date"] {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  color: #1e293b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-width: 150px;
}

.filter-form input[type="date"]:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.15);
  outline: none;
}

/* ===== TABLE STYLING ===== */
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

.table th {
  background-color: var(--bs-light, #f8f9fa);
  border-bottom: 2px solid var(--bs-primary, #0d6efd);
  font-weight: 600;
  color: var(--bs-dark, #212529);
  padding: 1rem 0.75rem;
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.diseases-list {
  max-width: 200px;
}

.diseases-list .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* ===== ACTION BUTTONS ===== */
.btn-group .btn {
  padding: 0.375rem 0.5rem;
  margin: 0 0.125rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* ===== PROFESSIONAL MODAL STYLING ===== */
.report-details-modal .modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Modal Header Professional */
.modal-header-professional {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  backdrop-filter: blur(10px);
}

.header-text {
  flex: 1;
}

.modal-title-main {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.modal-subtitle {
  margin: 0.25rem 0 0 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* Modal Body Professional */
.modal-body-professional {
  padding: 2rem;
  background: #f8fafc;
}

.info-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.section-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
}

.section-content {
  padding: 1.5rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.info-label i {
  width: 16px;
  text-align: center;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  padding: 0.5rem 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #e2e8f0;
}

.notes-text {
  font-style: italic;
  line-height: 1.6;
}

.phone-link {
  color: #059669;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}

.phone-link:hover {
  color: #047857;
  text-decoration: underline;
}

.diseases-display .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Modal Footer Professional */
.modal-footer-professional {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem 2rem;
}

.footer-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  align-items: center;
}

.btn-close-modal {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-close-modal:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== DARK MODE SUPPORT ===== */
[data-coreui-theme="dark"] .filter-form {
  background: linear-gradient(to right, #374151, #4b5563);
}

[data-coreui-theme="dark"] .filter-form label {
  color: #e5e7eb;
}

[data-coreui-theme="dark"] .filter-form input[type="date"] {
  background-color: #374151;
  border-color: #6b7280;
  color: #f9fafb;
}

[data-coreui-theme="dark"] .filter-form input[type="date"]:focus {
  border-color: #0ea5e9;
  background-color: #374151;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.25);
}

[data-coreui-theme="dark"] .table th {
  background-color: var(--bs-gray-700, #495057);
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .report-details-modal .modal-content {
  background: var(--bs-gray-800, #343a40);
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .modal-body-professional {
  background: var(--bs-gray-900, #212529);
}

[data-coreui-theme="dark"] .info-section {
  background: var(--bs-gray-800, #343a40);
  border: 1px solid var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .section-header {
  background: linear-gradient(135deg, var(--bs-gray-700, #495057) 0%, var(--bs-gray-600, #6c757d) 100%);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .section-title {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .info-label {
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .info-value {
  background: var(--bs-gray-700, #495057);
  color: var(--bs-light, #f8f9fa);
  border-left-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .modal-footer-professional {
  background: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
}

/* ===== PATIENT REPORTS MODAL STYLING ===== */
.patient-reports-modal .modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* No Reports Found Styling */
.no-reports-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
}

.no-reports-content {
  text-align: center;
  max-width: 500px;
}

.no-reports-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: #64748b;
}

.no-reports-title {
  color: #334155;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.no-reports-message {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.no-reports-suggestions {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.no-reports-suggestions h6 {
  color: #334155;
  font-weight: 600;
  margin-bottom: 1rem;
}

.no-reports-suggestions ul {
  margin: 0;
  padding-left: 1.5rem;
}

.no-reports-suggestions li {
  color: #64748b;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.no-reports-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn-add-report {
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-add-report:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.25);
}

/* Reports List Styling */
.patient-reports-list {
  padding: 1rem 0;
}

.reports-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.reports-count {
  margin: 0;
  color: #334155;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.reports-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.report-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  overflow: hidden;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.report-card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-date {
  display: flex;
  align-items: center;
  color: #334155;
  font-size: 0.9rem;
}

.btn-view-details {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.report-card-body {
  padding: 1.5rem;
}

.report-info-row {
  margin-bottom: 1rem;
}

.report-info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #64748b;
  display: flex;
  align-items: center;
}

.info-value {
  font-size: 0.95rem;
  color: #334155;
  padding: 0.5rem 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #e2e8f0;
}

.notes-preview {
  font-style: italic;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.diseases-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.diseases-badges .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* Footer Actions */
.btn-add-new-report {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-add-new-report:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.25);
}

/* ===== DARK MODE SUPPORT FOR PATIENT REPORTS ===== */
[data-coreui-theme="dark"] .patient-reports-modal .modal-content {
  background: var(--bs-gray-800, #343a40);
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .no-reports-icon {
  background: linear-gradient(135deg, var(--bs-gray-700, #495057) 0%, var(--bs-gray-600, #6c757d) 100%);
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .no-reports-title {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .no-reports-message {
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .no-reports-suggestions {
  background: var(--bs-gray-700, #495057);
  border: 1px solid var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .no-reports-suggestions h6 {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .no-reports-suggestions li {
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .reports-header {
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .reports-count {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .report-card {
  background: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .report-card-header {
  background: linear-gradient(135deg, var(--bs-gray-700, #495057) 0%, var(--bs-gray-600, #6c757d) 100%);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .report-date {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .info-label {
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .info-value {
  background: var(--bs-gray-700, #495057);
  color: var(--bs-light, #f8f9fa);
  border-left-color: var(--bs-gray-600, #6c757d);
}
