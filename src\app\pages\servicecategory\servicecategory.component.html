<div class="container-fluid py-4 bg-light min-vh-100">
  <div class="row g-4">
    <!-- Categories Section -->
    <div class="col-12 col-md-6 mb-4">
      <div class="card shadow-lg rounded-4 h-100 border-0 custom-table-card">
        <div class="card-header bg-primary text-white rounded-top-4 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center p-3">
          <h4 class="mb-2 mb-md-0 fw-bold">{{ 'serviceCategory.title' | translate | async }}</h4>
          <app-action-button
            *ngIf="!showCategoryForm"
            color="light"
            icon="cilPlus"
            text="serviceCategory.add"
            shape="rounded-pill"
            size="sm"
            (clicked)="showCategoryForm = true"
          ></app-action-button>
        </div>
        <div class="card-body p-4">
          <!-- Add/Edit Category Form -->
          <form [formGroup]="form" *ngIf="showCategoryForm" (ngSubmit)="submit()" class="mb-4 animated fadeIn">
            <div class="row g-3">
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.nameAr' | translate | async }}</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameAr">
                <div *ngIf="form.get('nameAr')?.invalid && form.get('nameAr')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.nameEn' | translate | async }}</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameEn">
                <div *ngIf="form.get('nameEn')?.invalid && form.get('nameEn')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.descriptionAr' | translate | async }}</label>
                <textarea class="form-control rounded-3" formControlName="descriptionAr" rows="3"></textarea>
                <div *ngIf="form.get('descriptionAr')?.invalid && form.get('descriptionAr')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.descriptionEn' | translate | async }}</label>
                <textarea class="form-control rounded-3" formControlName="descriptionEn" rows="3"></textarea>
                <div *ngIf="form.get('descriptionEn')?.invalid && form.get('descriptionEn')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
            </div>
            <div class="d-flex flex-column flex-md-row gap-2 align-items-stretch mt-3">
              <app-action-button
                color="primary"
                type="submit"
                [icon]="isEditMode ? 'cilPencil' : 'cilPlus'"
                [text]="isEditMode ? 'common.update' : 'common.add'"
                shape="rounded-pill"
                [disabled]="form.invalid"
              ></app-action-button>

              <app-action-button
                color="secondary"
                icon="cilX"
                text="common.cancel"
                shape="rounded-pill"
                (clicked)="cancelCategoryForm()"
              ></app-action-button>
            </div>
          </form>

          <!-- Search Bar -->
          <div class="search-container mb-4" *ngIf="!showCategoryForm">
            <div class="input-group">
              <span class="input-group-text bg-transparent border-end-0">
                <i class="bi bi-search"></i>
              </span>
              <input
                type="text"
                class="form-control border-start-0 ps-0"
                [placeholder]="'serviceCategory.searchPlaceholder' | translate | async"
                [(ngModel)]="searchKey"
                (keyup.enter)="onSearch()"
              >
              <app-action-button
                color="primary"
                icon="cilSearch"
                text="common.search"
                (clicked)="onSearch()"
              ></app-action-button>
            </div>
          </div>

          <!-- Categories List -->
          <div class="list-group list-group-flush custom-table" *ngIf="!showCategoryForm">
            <div *ngFor="let category of (categories$ | async)"
                 class="list-group-item d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center bg-white rounded-3 mb-2 shadow-hover border-0 p-3">
              <div class="d-flex align-items-center"
                   *ngIf="category.id != null"
                   (click)="selectCategory(category.id)"
                   [class.selected-item]="selectedCategoryId === category.id"
                   style="cursor:pointer;">
                <div class="category-icon me-3 d-flex align-items-center justify-content-center rounded-circle">
                  <i class="bi bi-folder-fill" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                  <h5 class="mb-0 fw-bold">{{ category.nameEn }}</h5>
                  <p class="text-muted small mb-0">{{ category.nameAr }}</p>
                </div>
              </div>
              <div class="d-flex flex-row gap-2 align-items-center justify-content-end mt-3 mt-md-0">
                <app-action-button
                  *ngIf="category.id != null"
                  color="info"
                  icon="cilList"
                  text="serviceCategory.viewSubCategories"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="selectCategory(category.id)"
                ></app-action-button>

                <app-action-button
                  color="primary"
                  icon="cilPencil"
                  text="common.edit"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="editCategory(category)"
                ></app-action-button>

                <app-action-button
                  *ngIf="category.id != null"
                  color="danger"
                  icon="cilTrash"
                  text="common.delete"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="openDeleteModal(category.id)"
                ></app-action-button>
              </div>
            </div>
            <div *ngIf="(categories$ | async)?.length === 0" class="empty-state p-4 text-center text-muted bg-white rounded-3 shadow-sm border-0">
              <i class="bi bi-folder2-open mb-3" style="font-size: 4rem;"></i>
              <h5>{{ 'serviceCategory.empty' | translate | async }}</h5>
              <p class="mb-0">{{ 'serviceCategory.emptyDescription' | translate | async }}</p>
            </div>
          </div>
  <app-pagination
        [currentPage]="currentPage"
        [totalItems]="totalItems$ | async"
        [pageSize]="pageSize"
        [layout]="'simple'"
        [showInfo]="true"
        [size]="'sm'"
        (pageChange)="onPageChange($event)"
      ></app-pagination>
          <!-- Pagination -->
          <!-- <div class="d-flex justify-content-center mt-4" *ngIf="!showCategoryForm">
            <c-pagination
              [activePage]="currentPage"
              [pages]="getPagesCount(totalItems$ | async)"
              [visiblePages]="5"
              (activePageChange)="onPageChange($event)"
              class="pagination-custom">
              <ng-container *ngFor="let page of [].constructor(getPagesCount(totalItems$ | async) || 0); let i = index">
                <c-pagination-item [active]="currentPage === i + 1" (click)="onPageChange(i + 1)">
                  {{ i + 1 }}
                </c-pagination-item>
              </ng-container>
              <c-pagination-item ariaLabel="Previous" [disabled]="currentPage === 1">
                <span aria-hidden="true">&laquo;</span>
              </c-pagination-item>
              <c-pagination-item ariaLabel="Next" [disabled]="currentPage === getPagesCount(totalItems$ | async)">
                <span aria-hidden="true">&raquo;</span>
              </c-pagination-item>
            </c-pagination>
          </div> -->
        </div>
      </div>
    </div>

    <!-- Subcategories Section -->
    <div class="col-12 col-md-6 mb-4" *ngIf="selectedCategoryId !== null">
      <div class="card shadow-lg rounded-4 h-100 border-0 custom-table-card animated fadeIn">
        <div class="card-header bg-success text-white rounded-top-4 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center p-3">
          <h4 class="mb-2 mb-md-0 fw-bold">{{ 'serviceCategory.subCategories' | translate | async }}</h4>
          <app-action-button
            *ngIf="!showSubCategoryForm"
            color="light"
            icon="cilPlus"
            text="serviceCategory.addSubCategory"
            shape="rounded-pill"
            size="sm"
            (clicked)="showSubCategoryForm = true"
          ></app-action-button>
        </div>
        <div class="card-body p-4">
          <!-- Add/Edit Subcategory Form -->
          <form [formGroup]="subCategoryForm" *ngIf="showSubCategoryForm && selectedCategoryId" (ngSubmit)="submitSubCategory()" class="mb-4 animated fadeIn">
            <div class="row g-3">
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.nameAr' | translate | async }}</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameAr">
                <div *ngIf="subCategoryForm.get('nameAr')?.invalid && subCategoryForm.get('nameAr')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.nameEn' | translate | async }}</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameEn">
                <div *ngIf="subCategoryForm.get('nameEn')?.invalid && subCategoryForm.get('nameEn')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.descriptionAr' | translate | async }}</label>
                <textarea class="form-control rounded-3" formControlName="descriptionAr" rows="3"></textarea>
                <div *ngIf="subCategoryForm.get('descriptionAr')?.invalid && subCategoryForm.get('descriptionAr')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.descriptionEn' | translate | async }}</label>
                <textarea class="form-control rounded-3" formControlName="descriptionEn" rows="3"></textarea>
                <div *ngIf="subCategoryForm.get('descriptionEn')?.invalid && subCategoryForm.get('descriptionEn')?.touched" class="text-danger mt-1 small">
                  {{ 'common.fieldRequired' | translate | async }}
                </div>
              </div>
              <div class="col-12 mb-3">
                <label class="form-label fw-semibold">{{ 'serviceCategory.icon' | translate | async }}</label>
                <div class="input-group">
                  <input type="file" class="form-control" (change)="onSubCategoryFileSelected($event)" accept="image/*" id="subCategoryIcon">
                  <label class="input-group-text" for="subCategoryIcon">
                    <i class="bi bi-cloud-upload-fill"></i>
                  </label>
                </div>
                <div *ngIf="subCategoryIconPreview" class="mt-3 preview-container d-flex flex-column align-items-center">
                  <div class="preview-image-container rounded-4 mb-2 border p-2">
                    <img [src]="subCategoryIconPreview" alt="Preview" class="preview-image">
                  </div>
                  <app-action-button
                    color="danger"
                    size="sm"
                    icon="cilTrash"
                    text="serviceCategory.iconRemove"
                    shape="rounded-pill"
                    (clicked)="removeSubCategoryIcon()"
                  ></app-action-button>
                </div>
              </div>
              <div class="col-12 mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="fromCallCenter" formControlName="fromCallCenter">
                  <label class="form-check-label fw-semibold" for="fromCallCenter">
                    {{ 'serviceCategory.fromCallCenter' | translate | async }}
                  </label>
                </div>
              </div>
            </div>
            <div class="d-flex flex-column flex-md-row gap-2 align-items-stretch mt-3">
              <app-action-button
                color="success"
                type="submit"
                [icon]="isEditSubMode ? 'cilPencil' : 'cilPlus'"
                [text]="isEditSubMode ? 'common.update' : 'common.add'"
                shape="rounded-pill"
                [disabled]="subCategoryForm.invalid"
              ></app-action-button>

              <app-action-button
                color="secondary"
                icon="cilX"
                text="common.cancel"
                shape="rounded-pill"
                (clicked)="cancelSubCategoryForm()"
              ></app-action-button>
            </div>
          </form>

          <!-- Subcategories List -->
          <div class="list-group list-group-flush custom-table" *ngIf="!showSubCategoryForm">
            <div *ngFor="let sub of (getSubCategories(selectedCategoryId) | async)" class="list-group-item d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center bg-white rounded-3 mb-2 shadow-hover border-0 p-3">
              <div class="d-flex align-items-center">
                <div class="subcategory-icon me-3 d-flex align-items-center justify-content-center rounded-circle">
                  <img *ngIf="sub.icon" [src]="getIconUrl(sub.icon)" alt="Icon" class="subcategory-icon-img">
                  <i *ngIf="!sub.icon" class="bi bi-layers-fill" style="font-size: 1.5rem;"></i>
                </div>
                <div>
                  <h5 class="mb-0 fw-bold">{{ sub.nameEn }}</h5>
                  <p class="text-muted small mb-0">{{ sub.nameAr }}</p>
                  <span *ngIf="sub.fromCallCenter" class="badge bg-info rounded-pill mt-1">
                    <i class="bi bi-telephone-fill me-1"></i> Call Center
                  </span>
                </div>
              </div>
              <div class="d-flex flex-row gap-2 align-items-center justify-content-end mt-3 mt-md-0">
                <app-action-button
                  color="primary"
                  icon="cilPencil"
                  text="common.edit"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="editSubCategory(sub)"
                ></app-action-button>

                <app-action-button
                  *ngIf="sub.id != null"
                  color="danger"
                  icon="cilTrash"
                  text="common.delete"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="openDeleteSubCategoryModal(sub.id)"
                ></app-action-button>
              </div>
            </div>
            <div *ngIf="(getSubCategories(selectedCategoryId) | async)?.length === 0" class="empty-state p-4 text-center text-muted bg-white rounded-3 shadow-sm border-0">
              <i class="bi bi-layers mb-3" style="font-size: 4rem;"></i>
              <h5>{{ 'serviceCategory.noSubCategories' | translate | async }}</h5>
              <p class="mb-0">{{ 'serviceCategory.noSubCategoriesDescription' | translate | async }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <c-modal [visible]="isDeleteModalOpen" (visibleChange)="!$event && closeDeleteModal()" class="fade-in">
    <c-modal-header class="bg-light rounded-top">
      <div class="d-flex align-items-center">
        <div class="modal-icon-container bg-danger-subtle rounded-circle p-2 me-2">
          <i class="bi bi-trash-fill text-danger"></i>
        </div>
        <h5 cModalTitle class="mb-0 fw-bold">{{ 'serviceCategory.deleteTitle' | translate | async }}</h5>
      </div>
      <button cButtonClose (click)="closeDeleteModal()"></button>
    </c-modal-header>
    <c-modal-body class="p-4">
      <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span>{{ 'serviceCategory.deleteConfirmation' | translate | async }}</span>
      </div>
    </c-modal-body>
    <c-modal-footer class="border-top-0">
      <app-action-button
        color="secondary"
        icon="cilX"
        text="common.cancel"
        shape="rounded-pill"
        (clicked)="closeDeleteModal()"
      ></app-action-button>

      <app-action-button
        color="danger"
        icon="cilTrash"
        text="common.delete"
        shape="rounded-pill"
        (clicked)="confirmDelete()"
      ></app-action-button>
    </c-modal-footer>
  </c-modal>

  <!-- Delete Subcategory Confirmation Modal -->
  <c-modal [visible]="isDeleteSubCategoryModalOpen" (visibleChange)="!$event && closeDeleteSubCategoryModal()" class="fade-in">
    <c-modal-header class="bg-light rounded-top">
      <div class="d-flex align-items-center">
        <div class="modal-icon-container bg-danger-subtle rounded-circle p-2 me-2">
          <i class="bi bi-trash-fill text-danger"></i>
        </div>
        <h5 cModalTitle class="mb-0 fw-bold">{{ 'serviceCategory.deleteSubCategoryTitle' | translate | async }}</h5>
      </div>
      <button cButtonClose (click)="closeDeleteSubCategoryModal()"></button>
    </c-modal-header>
    <c-modal-body class="p-4">
      <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span>{{ 'serviceCategory.deleteConfirmation' | translate | async }}</span>
      </div>
    </c-modal-body>
    <c-modal-footer class="border-top-0">
      <app-action-button
        color="secondary"
        icon="cilX"
        text="common.cancel"
        shape="rounded-pill"
        (clicked)="closeDeleteSubCategoryModal()"
      ></app-action-button>

      <app-action-button
        color="danger"
        icon="cilTrash"
        text="common.delete"
        shape="rounded-pill"
        (clicked)="confirmDeleteSubCategory()"
      ></app-action-button>
    </c-modal-footer>
  </c-modal>
</div>

<style>
  .category-item-hover:hover, .subcategory-item-hover:hover {
    background: #f8f9fa !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    transition: box-shadow 0.2s, background 0.2s;
  }
</style>
