<!-- Modern Login Page -->
<div class="login-container">
  <!-- Language Toggle -->
  <div class="language-toggle">
    <button
      type="button"
      class="lang-switch-btn"
      (click)="toggleLanguage()"
      [attr.aria-label]="'common.switchLanguage' | translate | async">
      <i class="bi bi-translate"></i>
      <span class="lang-text">
        {{ currentLanguage === 'en' ? 'العربية' : 'English' }}
      </span>
    </button>
  </div>

  <!-- Background Elements -->
  <div class="background-elements">
    <div class="bg-shape shape-1"></div>
    <div class="bg-shape shape-2"></div>
    <div class="bg-shape shape-3"></div>
  </div>

  <!-- Main Content -->
  <div class="login-content">
    <c-container>
      <c-row class="justify-content-center align-items-center min-vh-100">
        <c-col lg="5" md="7" sm="9" xs="11">

          <!-- Login Card -->
          <div class="login-card">

            <!-- Header Section -->
            <div class="login-header">
              <div class="logo-container">
                <div class="logo-icon">
                  <svg cIcon name="cilMedicalCross" size="xxl"></svg>
                </div>
              </div>
              <h1 class="login-title">{{ 'auth.login' | translate | async }}</h1>
              <p class="login-subtitle">{{ 'auth.signInMessage' | translate | async }}</p>
            </div>

            <!-- Form Section -->
            <div class="login-form-container">
              <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">

                <!-- Loading Spinner -->
                <div *ngIf="loading$ | async" class="loading-container">
                  <div class="modern-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                  </div>
                  <p class="loading-text">{{ 'common.loading' | translate | async }}</p>
                </div>

                <!-- Enhanced Error Messages with Translation -->
                <div *ngIf="loginError" class="error-alert">
                  <div class="error-icon">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                  </div>
                  <div class="error-content">
                    <h6 class="error-title">{{ 'auth.loginError' | translate | async }}</h6>
                    <p class="error-message">{{ loginError }}</p>
                  </div>
                </div>

                <div *ngIf="phoneError" class="error-alert phone-error">
                  <div class="error-icon">
                    <i class="bi bi-telephone-x-fill"></i>
                  </div>
                  <div class="error-content">
                    <h6 class="error-title">{{ 'validation.phoneError' | translate | async }}</h6>
                    <p class="error-message">{{ phoneError }}</p>
                  </div>
                </div>

                <!-- Phone Number Field -->
                <div class="form-group">
                  <label class="form-label">
                    <i class="bi bi-telephone-fill"></i>
                    {{ 'auth.phoneNumber' | translate | async }}
                  </label>
                  <div class="input-wrapper">
                    <input
                      cFormControl
                      formControlName="phonenumber"
                      class="modern-input"
                      [placeholder]="'auth.phoneNumberPlaceholder' | translate | async"
                      autocomplete="tel"
                      [ngClass]="{'error': hasFieldError('phonenumber')}"
                    />
                    <div class="input-focus-border"></div>
                  </div>
                  <div *ngIf="getFieldError('phonenumber')" class="field-error">
                    <i class="bi bi-exclamation-circle"></i>
                    <span>{{ getFieldError('phonenumber') }}</span>
                  </div>
                </div>

                <!-- Password Field with Visibility Toggle -->
                <div class="form-group">
                  <label class="form-label">
                    <i class="bi bi-lock-fill"></i>
                    {{ 'auth.password' | translate | async }}
                  </label>
                  <div class="input-wrapper password-wrapper">
                    <input
                      cFormControl
                      [type]="showPassword ? 'text' : 'password'"
                      formControlName="password"
                      class="modern-input password-input"
                      [placeholder]="'auth.passwordPlaceholder' | translate | async"
                      autocomplete="current-password"
                      [ngClass]="{'error': hasFieldError('password')}"
                    />
                    <button
                      type="button"
                      class="password-toggle-btn"
                      (click)="togglePasswordVisibility()"
                      [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
                      tabindex="-1">
                      <i [class]="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
                    </button>
                    <div class="input-focus-border"></div>
                  </div>
                  <div *ngIf="getFieldError('password')" class="field-error">
                    <i class="bi bi-exclamation-circle"></i>
                    <span>{{ getFieldError('password') }}</span>
                  </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="form-options">
                  <label class="remember-checkbox">
                    <input type="checkbox" class="checkbox-input">
                    <span class="checkbox-custom"></span>
                    <span class="checkbox-label">{{ 'auth.rememberMe' | translate | async }}</span>
                  </label>

                </div>

                <!-- Submit Button -->
                <button
                  type="submit"
                  class="login-button"
                  [disabled]="loginForm.invalid || !!(loading$ | async)"
                  [ngClass]="{'loading': loading$ | async}">
                  <span class="button-content">
                    <i class="bi bi-box-arrow-in-right button-icon"></i>
                    <span class="button-text">{{ 'auth.signIn' | translate | async }}</span>
                  </span>
                  <div class="button-loader">
                    <div class="loader-ring"></div>
                  </div>
                </button>

              </form>
            </div>

            

          </div>
        </c-col>
      </c-row>
    </c-container>
  </div>
</div>
