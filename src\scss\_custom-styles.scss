// Custom styles that need to be loaded after all @use statements
@use "./variables" as v;
@use "sass:color";
@use "./_shared-styles" as shared;

// Empty state styles
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;

  svg, i {
    color: #adb5bd;
    margin-bottom: 1rem;
  }
}

// Custom table responsive styles - using shared mixins
.table-container {
  @include shared.card-shadow;
  overflow: hidden;

  @media (max-width: 768px) {
    .custom-table {
      min-width: 800px;
    }

    .table-responsive {
      margin-bottom: 0;
    }
  }
}

// Color utility classes based on our theme
.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-success { background-color: var(--success) !important; }

.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-success { color: var(--success) !important; }

.border-primary { border-color: var(--primary) !important; }
.border-secondary { border-color: var(--secondary) !important; }
.border-success { border-color: var(--success) !important; }

// Apply to buttons
.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  @include shared.button-base;

  &:hover, &:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
  }
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
  @include shared.button-base;

  &:hover, &:focus {
    background-color: var(--success-dark);
    border-color: var(--success-dark);
  }
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: var(--primary);
  @include shared.button-base;

  &:hover, &:focus {
    background-color: #{color.scale(v.$secondary, $lightness: -5%)};
    border-color: #{color.scale(v.$secondary, $lightness: -5%)};
    color: var(--primary);
  }
}

// Link colors
a {
  color: var(--primary);
  &:hover {
    color: var(--primary-dark);
  }
}
