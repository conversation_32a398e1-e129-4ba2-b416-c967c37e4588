{"name": "coreui-free-angular-admin-template", "version": "5.4.5", "copyright": "Copyright 2024 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people) and contributors", "homepage": "https://coreui.io/angular", "config": {"theme": "default", "coreui_library_short_version": "5.4", "coreui_library_docs_url": "https://coreui.io/angular/docs/"}, "scripts": {"ng": "ng", "start": "ng serve -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.2", "@angular/cdk": "^19.2.3", "@angular/common": "^19.2.2", "@angular/compiler": "^19.2.2", "@angular/core": "^19.2.2", "@angular/fire": "^19.2.0", "@angular/forms": "^19.2.2", "@angular/language-service": "^19.2.2", "@angular/platform-browser": "^19.2.2", "@angular/platform-browser-dynamic": "^19.2.2", "@angular/router": "^19.2.2", "@coreui/angular": "~5.4.5", "@coreui/angular-chartjs": "^5.4.5", "@coreui/chartjs": "~4.1.0", "@coreui/coreui": "~5.3.1", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "~5.4.5", "@coreui/utils": "^2.0.2", "@fortawesome/fontawesome-free": "^6.7.2", "@ngrx/effects": "^19.1.0", "@ngrx/entity": "^19.1.0", "@ngrx/store": "^19.1.0", "@ngrx/store-devtools": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@types/leaflet": "^1.9.17", "bootstrap-icons": "^1.13.1", "chart.js": "^4.4.8", "firebase": "^11.8.1", "leaflet": "^1.9.4", "lodash-es": "^4.17.21", "ngx-scrollbar": "^13.0.3", "rxjs": "~7.8.2", "tslib": "^2.8.1", "update": "^0.4.2", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.1", "@angular-devkit/build-angular": "^19.2.3", "@angular/cli": "^19.2.3", "@angular/compiler-cli": "^19.2.2", "@angular/localize": "^19.2.2", "@types/jasmine": "^5.1.7", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "copy-webpack-plugin": "^13.0.0", "file-loader": "^6.2.0", "jasmine-core": "^5.6.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.7.3", "url-loader": "^4.1.1"}, "engines": {"node": "^18.19.1 || ^20.11.1 || ^22.0.0", "npm": ">= 9"}, "bugs": {"url": "https://github.com/coreui/coreui-free-angular-admin-template/issues"}, "repository": {"type": "git", "url": "git+https://github.com/coreui/coreui-free-angular-admin-template.git"}}