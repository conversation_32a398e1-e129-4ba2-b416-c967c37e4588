<div class="container-fluid py-4">
  <div class="row g-4">
    <!-- Governorates Section -->
    <div class="col-md-6">
      <div class="card custom-card shadow">
        <div class="card-header bg-primary text-white rounded-top-4 d-flex justify-content-between align-items-center">
          <h4 class="mb-0 fw-bold">{{ 'location.governorates' | translate | async }}</h4>
          <app-action-button
            *ngIf="!isAddingGovernorate"
            color="light"
            icon="cilPlus"
            text="location.addGovernorate"
            shape="rounded-pill"
            size="sm"
            (clicked)="isAddingGovernorate = true"
          ></app-action-button>
        </div>
        <div class="card-body p-4">
          <!-- Loading State -->
          <div *ngIf="loadingGovernorates" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">{{ 'location.loading' | translate | async }}</span>
            </div>
            <p class="text-muted mt-2">{{ 'location.loading' | translate | async }}</p>
          </div>

          <!-- Error State -->
          <div *ngIf="governorateError" class="alert alert-danger rounded-3 d-flex align-items-center" role="alert">
            <svg cIcon name="cilBan" class="me-2 flex-shrink-0"></svg>
            <div>{{ governorateError }}</div>
          </div>

          <!-- Add/Edit Governorate Form -->
          <form [formGroup]="governorateForm" *ngIf="isAddingGovernorate" class="mb-4 animated fadeIn">
            <div class="row g-3">
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'location.governorateName' | translate | async }} (AR)</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameAr">
                <div *ngIf="governorateForm.get('nameAr')?.invalid && governorateForm.get('nameAr')?.touched" class="text-danger mt-1 small">
                  {{ 'validation.required' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'location.governorateName' | translate | async }} (EN)</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameEn">
                <div *ngIf="governorateForm.get('nameEn')?.invalid && governorateForm.get('nameEn')?.touched" class="text-danger mt-1 small">
                  {{ 'validation.required' | translate | async }}
                </div>
              </div>
            </div>
            <div class="d-flex gap-2 mt-3">
              <app-action-button
                color="primary"
                [icon]="isEditingGovernorate ? 'cilPencil' : 'cilPlus'"
                [text]="isEditingGovernorate ? 'location.updateGovernorate' : 'location.addGovernorate'"
                shape="rounded-pill"
                [disabled]="!governorateForm.valid"
                (clicked)="isEditingGovernorate ? updateGovernorate() : addGovernorate()"
              ></app-action-button>

              <app-action-button
                color="secondary"
                icon="cilX"
                text="common.cancel"
                shape="rounded-pill"
                (clicked)="cancelEdit()"
              ></app-action-button>
            </div>
          </form>

          <!-- Governorates List -->
          <div class="list-group custom-table" *ngIf="!loadingGovernorates && !governorateError && !isAddingGovernorate">
            <div
              *ngFor="let gov of (governorates$ | async)"
              class="list-group-item d-flex justify-content-between align-items-center rounded-3 mb-2 p-3 shadow-hover border-0"
              [class.selected-item]="selectedGovernorateId === gov.id"
            >
              <div class="d-flex align-items-center">
                <div class="icon-container me-3">
                  <svg cIcon name="cilLocationPin"></svg>
                </div>
                <span class="fw-semibold">{{ currentLang === 'ar' ? gov.nameAr : gov.nameEn }}</span>
              </div>
              <div class="d-flex gap-2">
                <app-action-button
                  color="info"
                  icon="cilMap"
                  text="location.cities"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="onGovernorateClick(gov.id)"
                ></app-action-button>

                <app-action-button
                  color="primary"
                  icon="cilPencil"
                  text="common.edit"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="editGovernorate(gov)"
                ></app-action-button>

                <app-action-button
                  color="danger"
                  icon="cilTrash"
                  text="common.delete"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="openDeleteModal('governorate', gov.id)"
                ></app-action-button>
              </div>
            </div>
            <div *ngIf="(governorates$ | async)?.length === 0" class="empty-state p-4 text-center text-muted bg-white rounded-3 shadow-sm border-0">
              <svg cIcon name="cilLocationPin" size="3xl" class="mb-3"></svg>
              <h5>{{ 'location.noGovernorates' | translate | async }}</h5>
              <p class="mb-0">{{ 'location.addOneToStart' | translate | async }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cities Section -->
    <div class="col-md-6">
      <div class="card custom-card shadow">
        <div class="card-header bg-info text-white rounded-top-4 d-flex justify-content-between align-items-center">
          <h4 class="mb-0 fw-bold">{{ 'location.cities' | translate | async }}</h4>
          <app-action-button
            *ngIf="!isAddingCity && !isEditingCity"
            color="light"
            icon="cilPlus"
            text="location.addCity"
            shape="rounded-pill"
            size="sm"
            (clicked)="isAddingCity = true"
          ></app-action-button>
        </div>
        <div class="card-body p-4">
          <!-- Add/Edit City Form -->
          <form [formGroup]="cityForm" *ngIf="isAddingCity" class="mb-4 animated fadeIn">
            <div class="mb-4">
              <label for="governorate" class="form-label fw-semibold">{{ 'location.selectGovernorate' | translate | async }}</label>
              <select class="form-select rounded-pill" id="governorate" formControlName="governorateId">
                <option value="">{{ 'location.selectGovernorate' | translate | async }}</option>
                <option value="all">{{ 'location.allGovernorates' | translate | async }}</option>
                <ng-container *ngFor="let gov of (governorates$ | async)">
                  <option [value]="gov.id">{{ currentLang === 'ar' ? gov.nameAr : gov.nameEn }}</option>
                </ng-container>
              </select>
              <div *ngIf="cityForm.get('governorateId')?.invalid && cityForm.get('governorateId')?.touched" class="text-danger mt-1 small">
                {{ 'validation.required' | translate | async }}
              </div>
            </div>
            <div class="row g-3">
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'location.cityName' | translate | async }} (AR)</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameAr">
                <div *ngIf="cityForm.get('nameAr')?.invalid && cityForm.get('nameAr')?.touched" class="text-danger mt-1 small">
                  {{ 'validation.required' | translate | async }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-semibold">{{ 'location.cityName' | translate | async }} (EN)</label>
                <input type="text" class="form-control rounded-pill" formControlName="nameEn">
                <div *ngIf="cityForm.get('nameEn')?.invalid && cityForm.get('nameEn')?.touched" class="text-danger mt-1 small">
                  {{ 'validation.required' | translate | async }}
                </div>
              </div>
            </div>
            <div class="d-flex gap-2 mt-3">
              <app-action-button
                color="info"
                [icon]="isEditingCity ? 'cilPencil' : 'cilPlus'"
                [text]="isEditingCity ? 'location.updateCity' : 'location.addCity'"
                shape="rounded-pill"
                [disabled]="!cityForm.valid"
                (clicked)="isEditingCity ? updateCity() : addCity()"
              ></app-action-button>

              <app-action-button
                color="secondary"
                icon="cilX"
                text="common.cancel"
                shape="rounded-pill"
                (clicked)="cancelEdit()"
              ></app-action-button>
            </div>
          </form>

          <!-- Loading State -->
          <div *ngIf="loadingCities" class="text-center py-4">
            <div class="spinner-border text-info" role="status">
              <span class="visually-hidden">{{ 'location.loading' | translate | async }}</span>
            </div>
            <p class="text-muted mt-2">{{ 'location.loading' | translate | async }}</p>
          </div>

          <!-- Error State -->
          <div *ngIf="cityError" class="alert alert-danger rounded-3 d-flex align-items-center" role="alert">
            <svg cIcon name="cilBan" class="me-2 flex-shrink-0"></svg>
            <div>{{ cityError }}</div>
          </div>

          <!-- Cities List -->
          <div class="list-group custom-table" *ngIf="selectedGovernorateId && !loadingCities && !cityError && !isAddingCity">
            <div *ngFor="let city of (cities$ | async)" class="list-group-item d-flex justify-content-between align-items-center rounded-3 mb-2 p-3 shadow-hover border-0">
              <div class="d-flex align-items-center">
                <div class="icon-container info me-3">
                  <svg cIcon name="cilMap"></svg>
                </div>
                <span class="fw-semibold">{{ currentLang === 'ar' ? city.nameAr : city.nameEn }}</span>
              </div>
              <div class="d-flex gap-2">
                <app-action-button
                  color="primary"
                  icon="cilPencil"
                  text="common.edit"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="editCity(city)"
                ></app-action-button>

                <app-action-button
                  color="danger"
                  icon="cilTrash"
                  text="common.delete"
                  shape="rounded-pill"
                  size="sm"
                  (clicked)="openDeleteModal('city', city.id)"
                ></app-action-button>
              </div>
            </div>
            <div *ngIf="(cities$ | async)?.length === 0" class="empty-state p-4 text-center text-muted bg-white rounded-3 shadow-sm border-0">
              <svg cIcon name="cilMap" size="3xl" class="mb-3"></svg>
              <h5>{{ 'location.noCities' | translate | async }}</h5>
              <p class="mb-0">{{ 'location.addCityToStart' | translate | async }}</p>
            </div>
          </div>

          <div *ngIf="!selectedGovernorateId && !isAddingCity" class="empty-state p-4 text-center text-muted bg-white rounded-3 shadow-sm border-0">
            <svg cIcon name="cilLocationPin" size="3xl" class="mb-3"></svg>
            <h5>{{ 'location.selectGovernorateFirst' | translate | async }}</h5>
            <p class="mb-0">{{ 'location.selectGovernorateInstruction' | translate | async }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<c-modal [visible]="showDeleteModal" (visibleChange)="!$event && closeDeleteModal()" class="fade-in">
  <c-modal-header class="bg-light rounded-top">
    <div class="d-flex align-items-center">
      <div class="modal-icon-container bg-danger-subtle rounded-circle p-2 me-2">
        <svg cIcon name="cilTrash" class="text-danger"></svg>
      </div>
      <h5 cModalTitle class="mb-0 fw-bold">{{ 'common.delete' | translate | async }}</h5>
    </div>
    <button cButtonClose (click)="closeDeleteModal()"></button>
  </c-modal-header>
  <c-modal-body class="p-4">
    <div class="alert alert-warning">
      <svg cIcon name="cilWarning" class="me-2"></svg>
      <span>
        {{ deleteType === 'governorate'
          ? ('deleteGovernorateConfirm' | translate | async)
          : ('deleteCityConfirm' | translate | async) }}
      </span>
    </div>
  </c-modal-body>
  <c-modal-footer class="border-top-0">
    <app-action-button
      color="secondary"
      icon="cilX"
      text="common.cancel"
      shape="rounded-pill"
      (clicked)="closeDeleteModal()"
    ></app-action-button>

    <app-action-button
      color="danger"
      icon="cilTrash"
      text="common.delete"
      shape="rounded-pill"
      (clicked)="confirmDelete()"
    ></app-action-button>
  </c-modal-footer>
</c-modal>
