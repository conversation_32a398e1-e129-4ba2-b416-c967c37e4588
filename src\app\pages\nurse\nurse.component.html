<div class="container mt-4">
  <h2>{{ 'nurse.title' | translate | async }}</h2>

  <!-- Add Nurse Button -->
  <div class="mb-3" *ngIf="!showForm">
    <app-action-button
      color="primary"
      icon="cilPlus"
      text="nurse.add"
      shape="rounded-pill"
      (clicked)="showAddForm()"
    ></app-action-button>
  </div>

  <!-- Nurse Form -->
  <form *ngIf="showForm" [formGroup]="form" (ngSubmit)="submit()" class="mb-4 card p-3 shadow-sm">
    <div class="row g-3 align-items-center">
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.name' | translate | async }}</label>
        <div class="input-group">
          <input formControlName="firstName" placeholder="{{ 'nurse.name' | translate | async }}" class="form-control rounded-pill me-1" />
          <input formControlName="lastName" placeholder="{{ 'nurse.name' | translate | async }}" class="form-control rounded-pill" />
        </div>
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.phone' | translate | async }}</label>
        <input formControlName="phoneNumber" placeholder="{{ 'nurse.phone' | translate | async }}" class="form-control rounded-pill" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.specialization' | translate | async }}</label>
        <input formControlName="specialization" placeholder="{{ 'nurse.specialization' | translate | async }}" class="form-control rounded-pill" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.governorate' | translate | async }}</label>
        <input formControlName="governorate" placeholder="{{ 'nurse.governorate' | translate | async }}" class="form-control rounded-pill" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.city' | translate | async }}</label>
        <input formControlName="city" placeholder="{{ 'nurse.city' | translate | async }}" class="form-control rounded-pill" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.license' | translate | async }}</label>
        <input formControlName="licenseNumber" placeholder="{{ 'nurse.license' | translate | async }}" class="form-control rounded-pill" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.rate' | translate | async }}</label>
        <input formControlName="rate" placeholder="{{ 'nurse.rate' | translate | async }}" class="form-control rounded-pill" type="number" />
      </div>
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.image' | translate | async }}</label>
        <input type="file" (change)="onImageSelected($event)" class="form-control rounded-pill" accept="image/*" />
        <img *ngIf="imagePreview" [src]="imagePreview" alt="Preview" width="80" class="mt-2 rounded" />
      </div>

      <!-- Location Section -->
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label class="form-label mb-0">{{ 'nurse.location' | translate | async }}</label>
          <button type="button" class="btn btn-sm btn-outline-primary rounded-pill" (click)="toggleMap()">
            {{ showMap ? ('map.hideMap' | translate | async) : ('map.showMap' | translate | async) }}
          </button>
        </div>

        <!-- Map Selector with improved visibility handling -->
        <div class="map-wrapper mb-3" [ngClass]="{'d-block': showMap, 'd-none': !showMap}">
          <app-map-selector
            [initialLatitude]="form.get('latitude')?.value"
            [initialLongitude]="form.get('longitude')?.value"
            [visible]="showMap"
            (locationSelected)="onLocationSelected($event)"
            #mapSelector
          ></app-map-selector>
        </div>

        <!-- Coordinates Display -->
        <div class="row g-2" *ngIf="form.get('latitude')?.value || form.get('longitude')?.value">
          <div class="col-6">
            <div class="input-group input-group-sm">
              <span class="input-group-text">{{ 'map.latitude' | translate | async }}</span>
              <input type="text" class="form-control" [value]="form.get('latitude')?.value" readonly />
            </div>
          </div>
          <div class="col-6">
            <div class="input-group input-group-sm">
              <span class="input-group-text">{{ 'map.longitude' | translate | async }}</span>
              <input type="text" class="form-control" [value]="form.get('longitude')?.value" readonly />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-3 d-flex flex-column flex-md-row gap-2">
      <app-action-button
        color="primary"
        type="submit"
        [icon]="isEditMode ? 'cilPencil' : 'cilPlus'"
        [text]="isEditMode ? 'nurse.edit' : 'common.add'"
        shape="rounded-pill"
        [disabled]="form.invalid"
      ></app-action-button>

      <app-action-button
        color="secondary"
        icon="cilX"
        text="common.cancel"
        shape="rounded-pill"
        (clicked)="cancel()"
      ></app-action-button>
    </div>
  </form>

  <div *ngIf="successMessage" class="alert alert-success">{{ successMessage | translate | async }}</div>
  <div *ngIf="errorMessage" class="alert alert-danger">{{ errorMessage | translate | async }}</div>

  <div class="table-container">

    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>{{ 'nurse.image' | translate | async }}</th>
            <th>{{ 'nurse.name' | translate | async }}</th>
            <th>{{ 'nurse.phone' | translate | async }}</th>
            <th>{{ 'nurse.specialization' | translate | async }}</th>
            <th>{{ 'nurse.governorate' | translate | async }}</th>
            <th>{{ 'nurse.city' | translate | async }}</th>
            <th>{{ 'nurse.license' | translate | async }}</th>
            <th>{{ 'nurse.rate' | translate | async }}</th>
            <th class="actions-cell">{{ 'nurse.actions' | translate | async }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let nurse of nurses">
            <td>
              <img
                [src]="nurse.imageUrl"
                alt="nurse"
                class="avatar"
                (error)="handleImageError($event)"
              >
            </td>
            <td>{{ nurse.firstName }} {{ nurse.lastName }}</td>
            <td>{{ nurse.phoneNumber }}</td>
            <td>{{ nurse.specialization }}</td>
            <td>{{ nurse.governorate }}</td>
            <td>{{ nurse.city }}</td>
            <td>{{ nurse.licenseNumber }}</td>
            <td>{{ nurse.rate !== null ? nurse.rate : '-' }}</td>
            <td class="actions-cell">
              <div class="d-flex gap-2">
                <app-action-button
                  color="primary"
                  size="sm"
                  icon="cilPencil"
                  text="nurse.edit"
                  shape="rounded-pill"
                  (clicked)="editNurse(nurse)"
                ></app-action-button>

                <app-action-button
                  color="danger"
                  size="sm"
                  icon="cilTrash"
                  text="nurse.delete"
                  shape="rounded-pill"
                  (clicked)="openDeleteModal(nurse.id)"
                ></app-action-button>
              </div>
            </td>
          </tr>
          <tr *ngIf="nurses.length === 0" class="empty-row">
            <td colspan="9">{{ 'nurse.noData' | translate | async }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Delete Confirmation Modal (Bootstrap) -->
  <div class="modal fade show" tabindex="-1"
       [ngStyle]="{display: isDeleteModalOpen ? 'block' : 'none', background: 'rgba(0,0,0,0.4)'}"
       style="z-index: 2000; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-light">
          <h5 class="modal-title">{{ 'nurse.delete' | translate | async }}</h5>
          <button type="button" class="btn-close" (click)="closeDeleteModal()"></button>
        </div>
        <div class="modal-body">
          <p>{{ 'nurse.confirmDelete' | translate | async }}</p>
        </div>
        <div class="modal-footer">
          <app-action-button
            color="secondary"
            icon="cilX"
            text="common.cancel"
            (clicked)="closeDeleteModal()"
          ></app-action-button>

          <app-action-button
            color="danger"
            icon="cilTrash"
            text="nurse.delete"
            (clicked)="confirmDelete()"
          ></app-action-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Standardized Pagination -->
  <app-pagination
    [currentPage]="pageNumber"
    [totalItems]="totalCount"
    [pageSize]="pageSize"
    [layout]="'default'"
    [showInfo]="true"
    [size]="'md'"
    (pageChange)="onPageChange($event)"
  ></app-pagination>
</div>
