{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"coreui-free-angular-admin-template": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/coreui-free-angular-admin-template", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["@angular/localize/init", "zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "preserveSymlinks": true, "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/leaflet/dist/images/", "output": "assets/leaflet/"}], "styles": ["src/scss/styles.scss", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/leaflet/dist/leaflet.css", "node_modules/bootstrap-icons/font/bootstrap-icons.css"], "scripts": [], "allowedCommonJsDependencies": ["leaflet"], "stylePreprocessorOptions": {"sass": {"silenceDeprecations": []}, "includePaths": ["node_modules"]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "8mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "coreui-free-angular-admin-template:build:production"}, "development": {"buildTarget": "coreui-free-angular-admin-template:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "coreui-free-angular-admin-template:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": []}}}}}}