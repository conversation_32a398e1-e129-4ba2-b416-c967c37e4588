<div class="admin-container" [dir]="direction">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Admins</h2>
    <app-action-button
      color="primary"
      icon="cilSwapHorizontal"
      [text]="direction === 'ltr' ? 'RTL' : 'LTR'"
      (clicked)="toggleDirection()"
    ></app-action-button>
  </div>

  <div class="table-container">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Email</th>
            <th class="actions-cell">Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngIf="admins$ | async as admins">
            <tr *ngFor="let admin of admins?.items">
              <td>{{ admin.id }}</td>
              <td>{{ admin.name }}</td>
              <td>{{ admin.email }}</td>
              <td class="actions-cell">
                <div class="d-flex gap-2">
                  <app-action-button
                    color="primary"
                    size="sm"
                    icon="cilPencil"
                    text="common.edit"
                    shape="rounded-pill"
                  ></app-action-button>

                  <app-action-button
                    color="danger"
                    size="sm"
                    icon="cilTrash"
                    text="common.delete"
                    shape="rounded-pill"
                  ></app-action-button>
                </div>
              </td>
            </tr>
            <tr *ngIf="!admins?.items?.length" class="empty-row">
              <td colspan="4">No admins found</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Standardized Pagination -->
  <app-pagination
    [currentPage]="currentPage"
    [totalItems]="totalItems"
    [pageSize]="pageSize"
    [layout]="'default'"
    [showInfo]="true"
    [size]="'md'"
    [maxVisiblePages]="5"
    (pageChange)="onPageChange($event)"
  ></app-pagination>
</div>
