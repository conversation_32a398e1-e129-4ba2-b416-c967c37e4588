/* Leaflet Map Custom Styles */

/* Make sure Leaflet container takes the full dimensions of its parent */
.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  z-index: 1;
}

/* Fix marker z-index */
.leaflet-marker-pane {
  z-index: 600 !important;
}

.leaflet-tooltip-pane {
  z-index: 650 !important;
}

.leaflet-popup-pane {
  z-index: 700 !important;
}

/* Style for tooltips */
.leaflet-tooltip {
  background: white;
  border: none;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: 600;
  white-space: nowrap;
}

/* Style for the distance tooltip */
.distance-tooltip {
  background: white;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  padding: 5px 12px !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  color: #0c4a6e !important;
  white-space: nowrap;
  font-size: 12px !important;
  z-index: 1000 !important;
}

.distance-tooltip::before {
  display: none !important;
}

/* Leaflet Controls */
.leaflet-control-container .leaflet-control {
  z-index: 800 !important;
}

.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.leaflet-control-zoom a {
  color: #0c4a6e !important;
  width: 30px !important;
  height: 30px !important;
  line-height: 30px !important;
}

/* Popup styling */
.leaflet-popup-content-wrapper {
  border-radius: 8px !important;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2) !important;
}

.leaflet-popup-content {
  margin: 12px 16px !important;
  font-size: 13px !important;
}

.leaflet-popup-tip {
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2) !important;
}

/* Fix map attribution control */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.8) !important;
  padding: 2px 8px !important;
  font-size: 10px !important;
}

$leaflet-image-path: "../../node_modules/leaflet/dist/images/";

.leaflet-control-layers-toggle {
  background-image: url("#{$leaflet-image-path}layers.png");
}

@media (-webkit-min-device-pixel-ratio:2), (min-resolution:192dpi) {
  .leaflet-control-layers-toggle {
    background-image: url("#{$leaflet-image-path}layers-2x.png");
  }
}

.leaflet-marker-icon {
  background-image: url("#{$leaflet-image-path}marker-icon.png");
}

.leaflet-marker-shadow {
  background-image: url("#{$leaflet-image-path}marker-shadow.png");
}
